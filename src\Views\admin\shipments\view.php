<?php
// Shipment view page
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Shipment Details</h1>
    <div>
        <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Back to Shipments
        </a>
        <button type="button" class="btn btn-success" onclick="openEditModal(<?= $shipment['id'] ?>)">
            <i class="fas fa-edit"></i> Edit Shipment
        </button>
        <a href="<?= App\Core\View::url('/admin/shipments/updateStatus/' . $shipment['id']) ?>" class="btn btn-info" style="background-color: #17a2b8; color: white; border-color: #17a2b8;">
            <i class="fas fa-truck"></i> Update Status
        </a>
    </div>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">Shipment Information</div>
            <div class="card-body">
                <table class="table-container">
                    <tr>
                        <th>ID:</th>
                        <td><?= App\Core\View::e($shipment['id']) ?></td>
                    </tr>
                    <tr>
                        <th>Tracking Number:</th>
                        <td><?= App\Core\View::e($shipment['tracking_number']) ?></td>
                    </tr>
                    <tr>
                        <th>Status:</th>
                        <td>
                            <?php
                            $statusClass = 'bg-primary';
                            switch ($shipment['status']) {
                                case 'pending':
                                    $statusClass = 'bg-warning text-dark';
                                    break;
                                case 'in_transit':
                                    $statusClass = 'bg-info';
                                    break;
                                case 'out_for_delivery':
                                    $statusClass = 'bg-primary';
                                    break;
                                case 'delivered':
                                    $statusClass = 'bg-success';
                                    break;
                                case 'delayed':
                                    $statusClass = 'bg-danger';
                                    break;
                                case 'cancelled':
                                    $statusClass = 'bg-secondary';
                                    break;
                                case 'picked_up':
                                    $statusClass = 'bg-info';
                                    break;
                                case 'on_hold':
                                    $statusClass = 'bg-warning text-dark';
                                    break;
                                case 'returned':
                                    $statusClass = 'bg-danger';
                                    break;
                            }
                            ?>
                            <span class="badge <?= $statusClass ?>">
                                <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $shipment['status']))) ?>
                            </span>
                            <?php if (!empty($shipment['current_location'])): ?>
                                <div class="mt-2 small text-muted">
                                    <strong>Current Location:</strong> <?= App\Core\View::e($shipment['current_location']) ?>
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Origin:</th>
                        <td><?= App\Core\View::e($shipment['origin']) ?></td>
                    </tr>
                    <tr>
                        <th>Destination:</th>
                        <td><?= App\Core\View::e($shipment['destination']) ?></td>
                    </tr>
                    <tr>
                        <th>Weight:</th>
                        <td><?= isset($shipment['total_weight']) && $shipment['total_weight'] ? App\Core\View::e($shipment['total_weight']) . ' kg' : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Estimated Delivery:</th>
                        <td><?= isset($shipment['expected_delivery_date']) && $shipment['expected_delivery_date'] ? App\Core\View::e(date('Y-m-d', strtotime($shipment['expected_delivery_date']))) : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Created:</th>
                        <td><?= App\Core\View::e(date('Y-m-d H:i', strtotime($shipment['created_at']))) ?></td>
                    </tr>
                    <tr>
                        <th>Last Updated:</th>
                        <td><?= App\Core\View::e(date('Y-m-d H:i', strtotime($shipment['updated_at']))) ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">Notes</div>
            <div class="card-body">
                <?php if (empty($shipment['comments'])): ?>
                    <p class="text-muted">No notes available.</p>
                <?php else: ?>
                    <p><?= nl2br(App\Core\View::e($shipment['comments'])) ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">Sender Information</div>
            <div class="card-body">
                <table class="table-container">
                    <tr>
                        <th>Name:</th>
                        <td><?= App\Core\View::e($shipment['shipper_name'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Phone:</th>
                        <td><?= App\Core\View::e($shipment['shipper_phone'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Address:</th>
                        <td><?= nl2br(App\Core\View::e($shipment['shipper_address'] ?? 'N/A')) ?></td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td><?= App\Core\View::e($shipment['shipper_email'] ?? 'N/A') ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">Recipient Information</div>
            <div class="card-body">
                <table class="table-container">
                    <tr>
                        <th>Name:</th>
                        <td><?= App\Core\View::e($shipment['receiver_name'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Phone:</th>
                        <td><?= App\Core\View::e($shipment['receiver_phone'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Address:</th>
                        <td><?= nl2br(App\Core\View::e($shipment['receiver_address'] ?? 'N/A')) ?></td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td><?= App\Core\View::e($shipment['receiver_email'] ?? 'N/A') ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span>Shipment History</span>
        <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#addHistoryModal">
            <i class="fas fa-plus"></i> Add History Entry
        </button>
    </div>
    <div class="card-body">
        <?php if (empty($history)): ?>
            <p class="text-muted">No history entries found.</p>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Date & Time</th>
                            <th>Status</th>
                            <th>Location</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($history as $entry): ?>
                            <tr>
                                <td><?= App\Core\View::e(date('Y-m-d H:i', strtotime($entry['date_time'] ?? $entry['timestamp']))) ?></td>
                                <td>
                                    <?php
                                    $statusClass = 'bg-primary';
                                    switch ($entry['status']) {
                                        case 'pending':
                                            $statusClass = 'bg-warning text-dark';
                                            break;
                                        case 'in_transit':
                                            $statusClass = 'bg-info';
                                            break;
                                        case 'out_for_delivery':
                                            $statusClass = 'bg-primary';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'bg-success';
                                            break;
                                        case 'delayed':
                                            $statusClass = 'bg-danger';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-secondary';
                                            break;
                                        case 'picked_up':
                                            $statusClass = 'bg-info';
                                            break;
                                        case 'on_hold':
                                            $statusClass = 'bg-warning text-dark';
                                            break;
                                        case 'returned':
                                            $statusClass = 'bg-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?>">
                                        <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $entry['status']))) ?>
                                    </span>
                                </td>
                                <td><?= App\Core\View::e($entry['location']) ?></td>
                                <td><?= App\Core\View::e($entry['message'] ?? $entry['description']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Document Management Section -->
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span>Document Management</span>
        <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#requestDocumentModal">
            <i class="fas fa-file-plus"></i> Request Document
        </button>
    </div>
    <div class="card-body">
        <div id="document-requests-list">
            <div class="text-center">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Loading document requests...</span>
            </div>
        </div>
    </div>
</div>

<!-- Add History Modal -->
<div class="modal" id="addHistoryModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Shipment Status</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?= App\Core\View::url('/admin/shipments/add-history/' . $shipment['id']) ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status_date">Date</label>
                                <input type="date" id="status_date" name="status_date" class="form-control"
                                    value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status_time">Time</label>
                                <input type="time" id="status_time" name="status_time" class="form-control"
                                    value="<?= date('H:i') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location" class="form-control"
                            value="<?= App\Core\View::e($shipment['destination']) ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" class="form-control" required>
                            <option value="">-- Select Status --</option>
                            <?php
                            // Use status options from database if available
                            if (isset($statusOptions) && !empty($statusOptions)) {
                                foreach ($statusOptions as $option):
                                    if (!$option['is_active']) continue;
                                    $value = $option['name'];
                                    $label = ucfirst(str_replace('_', ' ', $option['name']));
                                    $selected = $shipment['status'] === $value ? 'selected' : '';
                            ?>
                                <option value="<?= $value ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php
                                endforeach;
                            } else {
                                // Fallback to hardcoded statuses
                                $statuses = ['pending' => 'Pending', 'in_transit' => 'In Transit', 'out_for_delivery' => 'Out for Delivery', 'delivered' => 'Delivered', 'delayed' => 'Delayed', 'cancelled' => 'Cancelled'];
                                foreach ($statuses as $value => $label):
                            ?>
                                <option value="<?= $value ?>" <?= $shipment['status'] === $value ? 'selected' : '' ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php
                                endforeach;
                            }
                            ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Remarks</label>
                        <textarea id="description" name="description" class="form-control" rows="3"
                            placeholder="Enter any additional information about this status update"></textarea>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="notify_receiver" name="notify_receiver" value="1" checked>
                        <label class="form-check-label" for="notify_receiver">Notify receiver about this update</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Request Document Modal -->
<div class="modal" id="requestDocumentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Request Document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="requestDocumentForm" action="<?= App\Core\View::url('/admin/shipments/request-document/' . $shipment['id']) ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="document_type_id">Document Type</label>
                                <select id="document_type_id" name="document_type_id" class="form-control" required>
                                    <option value="">-- Select Document Type --</option>
                                    <!-- Options will be loaded via JavaScript from API -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="priority">Priority</label>
                                <select id="priority" name="priority" class="form-control" required>
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="due_date">Due Date (Optional)</label>
                        <input type="datetime-local" id="due_date" name="due_date" class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="request_message">Request Message</label>
                        <textarea id="request_message" name="request_message" class="form-control" rows="4"
                                  placeholder="Explain why this document is needed and any specific requirements..." required></textarea>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="send_notification" name="send_notification" value="1" checked>
                        <label class="form-check-label" for="send_notification">Send email notification to recipient</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Request Document</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Document Review Modal -->
<div class="modal" id="reviewDocumentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="reviewDocumentForm" method="POST">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-body">
                    <div id="document-preview">
                        <!-- Document preview will be loaded here -->
                    </div>

                    <div class="form-group mt-3">
                        <label for="review_notes">Review Notes</label>
                        <textarea id="review_notes" name="review_notes" class="form-control" rows="3"
                                  placeholder="Add any notes about your decision..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="reviewDocument('rejected')">
                        <i class="fas fa-times"></i> Reject
                    </button>
                    <button type="button" class="btn btn-success" onclick="reviewDocument('approved')">
                        <i class="fas fa-check"></i> Approve
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Simple modal functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Show modal
        const modalTriggers = document.querySelectorAll('[data-toggle="modal"]');
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const modal = document.querySelector(targetId);
                if (modal) {
                    modal.style.display = 'block';
                }
            });
        });

        // Close modal
        const closeBtns = document.querySelectorAll('[data-dismiss="modal"]');
        closeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
    });
</script>

<?php include __DIR__ . '/partials/_edit_modal.php'; ?>

<script>
    function openEditModal(shipmentId) {
        // Open the modal
        const modal = new bootstrap.Modal(document.getElementById('editShipmentModal'));
        modal.show();

        // Load the shipment data
        window.loadShipmentForEdit(shipmentId);
    }

    // Document Management Functions
    let currentDocumentRequestId = null;
    let currentDocumentUploadId = null;

    document.addEventListener('DOMContentLoaded', function() {
        loadDocumentTypes();
        loadDocumentRequests();

        // Handle document request form submission
        document.getElementById('requestDocumentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            submitDocumentRequest();
        });
    });

    async function loadDocumentTypes() {
        try {
            const response = await fetch('<?= App\Core\View::url('/api/document-types.php') ?>');
            const result = await response.json();

            const select = document.getElementById('document_type_id');
            select.innerHTML = '<option value="">-- Select Document Type --</option>';

            if (result.success && result.types) {
                result.types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    if (type.description) {
                        option.title = type.description;
                    }
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading document types:', error);
        }
    }

    async function loadDocumentRequests() {
        try {
            const shipmentId = <?= $shipment['id'] ?>;
            const response = await fetch(`<?= App\Core\View::url('/api/admin-document-requests.php') ?>?shipment_id=${shipmentId}`);
            const result = await response.json();

            const container = document.getElementById('document-requests-list');

            if (result.success && result.requests.length > 0) {
                let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
                html += '<th>Document Type</th><th>Priority</th><th>Status</th><th>Due Date</th><th>Requested</th><th>Actions</th>';
                html += '</tr></thead><tbody>';

                result.requests.forEach(request => {
                    const priorityClass = getPriorityClass(request.priority);
                    const statusClass = getStatusClass(request.status);
                    const isOverdue = request.due_date && new Date(request.due_date) < new Date();

                    html += `<tr ${isOverdue ? 'class="table-warning"' : ''}>`;
                    html += `<td>${request.document_type_name}</td>`;
                    html += `<td><span class="badge ${priorityClass}">${request.priority.toUpperCase()}</span></td>`;
                    html += `<td><span class="badge ${statusClass}">${formatStatus(request.status)}</span></td>`;
                    html += `<td>${request.due_date ? formatDate(request.due_date) : 'No deadline'}</td>`;
                    html += `<td>${formatDate(request.created_at)}</td>`;
                    html += `<td>${generateActionButtons(request)}</td>`;
                    html += '</tr>';
                });

                html += '</tbody></table></div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<p class="text-muted">No document requests found.</p>';
            }
        } catch (error) {
            console.error('Error loading document requests:', error);
            document.getElementById('document-requests-list').innerHTML =
                '<p class="text-danger">Error loading document requests.</p>';
        }
    }

    function generateActionButtons(request) {
        let buttons = '';

        switch (request.status) {
            case 'pending':
                buttons += `<button class="btn btn-sm btn-warning" onclick="cancelDocumentRequest(${request.id})">
                    <i class="fas fa-times"></i> Cancel
                </button>`;
                break;

            case 'uploaded':
                buttons += `<button class="btn btn-sm btn-info" onclick="reviewDocument(${request.id}, ${request.uploaded_document?.id})">
                    <i class="fas fa-eye"></i> Review
                </button>`;
                break;

            case 'approved':
            case 'rejected':
                buttons += `<button class="btn btn-sm btn-secondary" onclick="viewDocumentHistory(${request.id})">
                    <i class="fas fa-history"></i> History
                </button>`;
                break;
        }

        return buttons;
    }

    function getPriorityClass(priority) {
        const classes = {
            'low': 'badge-secondary',
            'medium': 'badge-warning',
            'high': 'badge-danger',
            'urgent': 'badge-dark'
        };
        return classes[priority] || 'badge-secondary';
    }

    function getStatusClass(status) {
        const classes = {
            'pending': 'badge-warning',
            'uploaded': 'badge-info',
            'approved': 'badge-success',
            'rejected': 'badge-danger',
            'cancelled': 'badge-secondary'
        };
        return classes[status] || 'badge-secondary';
    }

    function formatStatus(status) {
        const labels = {
            'pending': 'Pending Upload',
            'uploaded': 'Under Review',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'cancelled': 'Cancelled'
        };
        return labels[status] || status;
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    async function submitDocumentRequest() {
        try {
            const form = document.getElementById('requestDocumentForm');
            const formData = new FormData(form);

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Close modal
                document.getElementById('requestDocumentModal').style.display = 'none';

                // Reload document requests
                loadDocumentRequests();

                // Show success message
                showAlert('Document request sent successfully!', 'success');

                // Reset form
                form.reset();
            } else {
                showAlert(result.message || 'Error sending document request', 'danger');
            }
        } catch (error) {
            console.error('Error submitting document request:', error);
            showAlert('Error sending document request', 'danger');
        }
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;

        // Insert at the top of the page
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
</script>
