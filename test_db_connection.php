<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Database connection successful\n";
    
    // Check if tables exist
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Tables found:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    // Check if document_types table exists and has data
    if (in_array('document_types', $tables)) {
        $stmt = $pdo->query('SELECT COUNT(*) as count FROM document_types');
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "\nDocument types count: " . $result['count'] . "\n";
    } else {
        echo "\nDocument types table does not exist\n";
    }
    
    // Check if users table exists and has data
    if (in_array('users', $tables)) {
        $stmt = $pdo->query('SELECT COUNT(*) as count FROM users');
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Users count: " . $result['count'] . "\n";
    } else {
        echo "Users table does not exist\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
