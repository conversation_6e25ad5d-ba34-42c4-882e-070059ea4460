<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    
    echo "Checking created tables:\n\n";
    
    // Check for document-related tables
    $tables_to_check = [
        'document_types',
        'document_requests', 
        'document_uploads',
        'document_approvals',
        'notifications'
    ];
    
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->fetch();
        
        if ($exists) {
            echo "✓ Table '$table' exists\n";
            
            // Show structure
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "  Columns: " . implode(', ', array_column($columns, 'Field')) . "\n";
            
            // Show row count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "  Rows: $count\n\n";
        } else {
            echo "✗ Table '$table' does not exist\n\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
