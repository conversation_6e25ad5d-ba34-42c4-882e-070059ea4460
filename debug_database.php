<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Database Debug Report ===\n\n";

try {
    // Test database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Database connection successful\n\n";
    
    // Check tables
    echo "=== TABLES ===\n";
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "❌ No tables found in database!\n";
    } else {
        foreach ($tables as $table) {
            echo "✓ $table\n";
        }
    }
    
    echo "\n=== TABLE CHECKS ===\n";
    
    // Check document_types table
    if (in_array('document_types', $tables)) {
        $stmt = $pdo->query('SELECT COUNT(*) as count FROM document_types');
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ document_types table exists with {$result['count']} records\n";
        
        // Show sample data
        $stmt = $pdo->query('SELECT id, name FROM document_types LIMIT 3');
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($types as $type) {
            echo "  - {$type['id']}: {$type['name']}\n";
        }
    } else {
        echo "❌ document_types table missing\n";
    }
    
    // Check users table
    if (in_array('users', $tables)) {
        $stmt = $pdo->query('SELECT COUNT(*) as count FROM users');
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ users table exists with {$result['count']} records\n";
    } else {
        echo "❌ users table missing\n";
    }
    
    // Check document_requests table
    if (in_array('document_requests', $tables)) {
        $stmt = $pdo->query('SELECT COUNT(*) as count FROM document_requests');
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ document_requests table exists with {$result['count']} records\n";
    } else {
        echo "❌ document_requests table missing\n";
    }
    
    // Check shipments table
    if (in_array('shipments', $tables)) {
        $stmt = $pdo->query('SELECT COUNT(*) as count FROM shipments');
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ shipments table exists with {$result['count']} records\n";
    } else {
        echo "❌ shipments table missing\n";
    }
    
    echo "\n=== API ENDPOINT TESTS ===\n";
    
    // Test document-types API endpoint
    echo "Testing document-types API...\n";
    $url = 'http://localhost/courier/public/api/admin/document-types.php';
    
    // Create a context for the request
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "Content-Type: application/json\r\n",
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response === false) {
        echo "❌ Failed to reach document-types API\n";
        $error = error_get_last();
        echo "Error: " . ($error['message'] ?? 'Unknown error') . "\n";
    } else {
        echo "✓ document-types API responded\n";
        echo "Response: " . substr($response, 0, 200) . "...\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
}

echo "\n=== END REPORT ===\n";
?>
