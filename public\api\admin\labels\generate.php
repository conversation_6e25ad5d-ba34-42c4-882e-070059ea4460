<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 4));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\App;
use App\Core\Database;
use App\Core\Session;
use App\Core\View;

// Initialize the application
$app = App::getInstance();

// Set appropriate headers
if (isset($_POST['download']) && $_POST['download'] === '1') {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="shipping-label.pdf"');
} else {
    header('Content-Type: application/json');
}

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    if (!isset($_POST['download'])) {
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    }
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get form data
        $shipment_id = $_POST['shipment_id'] ?? null;
        $label_type = $_POST['label_type'] ?? 'standard';
        $label_size = $_POST['label_size'] ?? '4x6';
        $include_logo = isset($_POST['include_logo']) ? true : false;
        $include_barcode = isset($_POST['include_barcode']) ? true : false;
        $include_return_address = isset($_POST['include_return_address']) ? true : false;
        
        if (!$shipment_id) {
            if (isset($_POST['download'])) {
                http_response_code(400);
                echo "Shipment ID is required";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment ID is required']);
                exit;
            }
        }
        
        // Get shipment data
        Database::prepare("SELECT * FROM shipments WHERE id = :id");
        Database::bindValue(':id', $shipment_id, PDO::PARAM_INT);
        Database::execute();
        $shipment = Database::fetch();
        
        if (!$shipment) {
            if (isset($_POST['download'])) {
                http_response_code(404);
                echo "Shipment not found";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment not found']);
                exit;
            }
        }
        
        // Generate label HTML
        $labelHtml = generateLabelHtml($shipment, [
            'label_type' => $label_type,
            'label_size' => $label_size,
            'include_logo' => $include_logo,
            'include_barcode' => $include_barcode,
            'include_return_address' => $include_return_address
        ]);
        
        if (isset($_POST['download']) && $_POST['download'] === '1') {
            // Generate PDF and return as download
            $pdf = generateLabelPdf($labelHtml, $shipment);
            echo $pdf;
        } else {
            // Return HTML for preview
            echo json_encode([
                'success' => true,
                'html' => $labelHtml,
                'shipment_id' => $shipment_id,
                'tracking_number' => $shipment['tracking_number']
            ]);
        }
        
    } else {
        http_response_code(405);
        if (!isset($_POST['download'])) {
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        }
    }
    
} catch (Exception $e) {
    error_log("Error in label generation API: " . $e->getMessage());
    http_response_code(500);
    if (isset($_POST['download'])) {
        echo "Error generating label: " . $e->getMessage();
    } else {
        echo json_encode(['success' => false, 'message' => 'Error generating label: ' . $e->getMessage()]);
    }
}

function generateLabelHtml($shipment, $options) {
    $html = '
    <div class="shipping-label" style="width: 400px; height: 600px; border: 2px solid #000; padding: 20px; font-family: Arial, sans-serif; background: white;">
        <div class="label-header" style="text-align: center; margin-bottom: 20px;">
            ' . ($options['include_logo'] ? '<div class="logo" style="font-size: 24px; font-weight: bold; color: #333;">ELTA COURIER</div>' : '') . '
            <div class="label-type" style="font-size: 14px; margin-top: 10px;">' . strtoupper($options['label_type']) . ' SHIPPING LABEL</div>
        </div>
        
        <div class="tracking-section" style="margin-bottom: 20px; text-align: center;">
            <div class="tracking-number" style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">
                ' . htmlspecialchars($shipment['tracking_number']) . '
            </div>
            ' . ($options['include_barcode'] ? '<div class="barcode" style="height: 40px; background: repeating-linear-gradient(90deg, #000 0px, #000 2px, #fff 2px, #fff 4px); margin: 10px 0;"></div>' : '') . '
        </div>
        
        <div class="addresses" style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div class="from-address" style="width: 45%;">
                <div style="font-weight: bold; margin-bottom: 5px;">FROM:</div>
                <div style="font-size: 12px;">
                    ' . htmlspecialchars($shipment['shipper_name']) . '<br>
                    ' . nl2br(htmlspecialchars($shipment['shipper_address'])) . '
                </div>
            </div>
            
            <div class="to-address" style="width: 45%;">
                <div style="font-weight: bold; margin-bottom: 5px;">TO:</div>
                <div style="font-size: 12px;">
                    ' . htmlspecialchars($shipment['receiver_name']) . '<br>
                    ' . nl2br(htmlspecialchars($shipment['receiver_address'])) . '
                </div>
            </div>
        </div>
        
        <div class="shipment-details" style="border-top: 1px solid #ccc; padding-top: 15px; font-size: 11px;">
            <div style="margin-bottom: 5px;"><strong>Service:</strong> ' . htmlspecialchars($shipment['shipment_type'] ?? 'Standard') . '</div>
            <div style="margin-bottom: 5px;"><strong>Weight:</strong> ' . htmlspecialchars($shipment['total_weight'] ?? 'N/A') . ' kg</div>
            <div style="margin-bottom: 5px;"><strong>Date:</strong> ' . date('Y-m-d H:i') . '</div>
        </div>
        
        ' . ($options['include_return_address'] ? '
        <div class="return-address" style="border-top: 1px solid #ccc; margin-top: 15px; padding-top: 10px; font-size: 10px;">
            <strong>Return Address:</strong><br>
            ELTA Courier Services<br>
            123 Shipping Lane<br>
            Logistics City, LC 12345
        </div>
        ' : '') . '
    </div>';
    
    return $html;
}

function generateLabelPdf($html, $shipment) {
    // For now, return a simple PDF placeholder
    // In a real implementation, you would use a library like TCPDF or DomPDF
    $pdf_content = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Shipping Label - " . $shipment['tracking_number'] . ") Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF";
    
    return $pdf_content;
}
?>
