<?php
$pageTitle = 'Generate Invoice';
$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => '/admin/dashboard'],
    ['title' => 'Shipments', 'url' => '/admin/shipments'],
    ['title' => 'Generate Invoice', 'url' => '']
];
?>

<div class="page-header">
    <div class="page-header-content">
        <h1 class="page-title">
            <i class="fas fa-file-invoice"></i>
            Generate Invoice
        </h1>
        <p class="page-description">Generate professional invoices for shipments</p>
    </div>
    <div class="page-actions">
        <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Back to Shipments
        </a>
    </div>
</div>

<div class="content-wrapper">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-search"></i>
                        Select Shipment
                    </h3>
                </div>
                <div class="card-body">
                    <form id="invoiceForm">
                        <?= App\Core\View::csrfField() ?>
                        
                        <div class="form-group">
                            <label for="shipment_search">Search Shipment</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="shipment_search" 
                                       placeholder="Enter tracking number or client name">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-primary" onclick="searchShipments()">
                                        <i class="fas fa-search"></i>
                                        Search
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div id="shipment_results" class="shipment-results" style="display: none;">
                            <h5>Search Results:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead>
                                        <tr>
                                            <th>Select</th>
                                            <th>Tracking #</th>
                                            <th>Client</th>
                                            <th>Origin</th>
                                            <th>Destination</th>
                                            <th>Status</th>
                                            <th>Cost</th>
                                        </tr>
                                    </thead>
                                    <tbody id="shipment_results_body">
                                        <!-- Results will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div id="selected_shipment" class="selected-shipment" style="display: none;">
                            <h5>Selected Shipment:</h5>
                            <div class="alert alert-info">
                                <strong>Tracking #:</strong> <span id="selected_tracking"></span><br>
                                <strong>Client:</strong> <span id="selected_client"></span><br>
                                <strong>Route:</strong> <span id="selected_route"></span><br>
                                <strong>Total Cost:</strong> $<span id="selected_cost"></span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="invoice_type">Invoice Type</label>
                            <select class="form-control" id="invoice_type" name="invoice_type" required>
                                <option value="">Select invoice type</option>
                                <option value="standard">Standard Invoice</option>
                                <option value="detailed">Detailed Invoice</option>
                                <option value="commercial">Commercial Invoice</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="invoice_date">Invoice Date</label>
                            <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="due_date">Due Date</label>
                            <input type="date" class="form-control" id="due_date" name="due_date" 
                                   value="<?= date('Y-m-d', strtotime('+30 days')) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="additional_charges">Additional Charges</label>
                            <div id="additional_charges_container">
                                <div class="charge-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" name="charge_description[]" 
                                                   placeholder="Description (e.g., Insurance, Handling Fee)">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="number" class="form-control" name="charge_amount[]" 
                                                   placeholder="Amount" step="0.01">
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-danger btn-sm" onclick="removeCharge(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-secondary mt-2" onclick="addCharge()">
                                <i class="fas fa-plus"></i>
                                Add Charge
                            </button>
                        </div>
                        
                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="Additional notes for the invoice"></textarea>
                        </div>
                        
                        <input type="hidden" id="selected_shipment_id" name="shipment_id">
                        
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" onclick="generateInvoice()">
                                <i class="fas fa-file-invoice"></i>
                                Generate Invoice
                            </button>
                            <button type="button" class="btn btn-success" onclick="generateAndDownload()">
                                <i class="fas fa-download"></i>
                                Generate & Download PDF
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        Invoice Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>Invoice Types:</strong>
                        <ul class="mt-2">
                            <li><strong>Standard:</strong> Basic invoice with shipment details</li>
                            <li><strong>Detailed:</strong> Includes package breakdown and timeline</li>
                            <li><strong>Commercial:</strong> For international shipments with customs info</li>
                        </ul>
                    </div>
                    
                    <div class="info-item mt-3">
                        <strong>Features:</strong>
                        <ul class="mt-2">
                            <li>Professional PDF generation</li>
                            <li>Company branding and logo</li>
                            <li>Automatic calculations</li>
                            <li>Tax and fee breakdown</li>
                            <li>Payment terms and conditions</li>
                        </ul>
                    </div>
                    
                    <div class="info-item mt-3">
                        <strong>Recent Invoices:</strong>
                        <div id="recent_invoices" class="mt-2">
                            <p class="text-muted">No recent invoices</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Preview Modal -->
<div id="invoicePreviewModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Invoice Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="invoice_preview_content">
                    <!-- Invoice preview will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="downloadInvoicePDF()">
                    <i class="fas fa-download"></i>
                    Download PDF
                </button>
                <button type="button" class="btn btn-success" onclick="emailInvoice()">
                    <i class="fas fa-envelope"></i>
                    Email Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-header-content h1 {
    margin: 0;
    color: var(--primary-color);
}

.page-header-content p {
    margin: 5px 0 0 0;
    color: #6c757d;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-title {
    margin: 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

.shipment-results {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.selected-shipment {
    margin-top: 20px;
}

.charge-item {
    margin-bottom: 10px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.info-item {
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-item ul {
    margin-bottom: 0;
    padding-left: 20px;
}

.info-item li {
    margin-bottom: 5px;
}

.btn-group {
    margin-right: 10px;
}

.table-sm th,
.table-sm td {
    padding: 8px;
}

.alert {
    margin-bottom: 0;
}

#invoice_preview_content {
    max-height: 500px;
    overflow-y: auto;
}
</style>

<script>
let selectedShipmentData = null;

document.addEventListener('DOMContentLoaded', function() {
    loadRecentInvoices();
});

function searchShipments() {
    const searchTerm = document.getElementById('shipment_search').value.trim();
    
    if (!searchTerm) {
        alert('Please enter a tracking number or client name to search');
        return;
    }
    
    fetch(`<?= App\Core\View::url('/api/admin/shipments/search.php') ?>?q=${encodeURIComponent(searchTerm)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayShipmentResults(data.shipments);
            } else {
                alert('Error searching shipments: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error searching shipments:', error);
            alert('An error occurred while searching shipments.');
        });
}

function displayShipmentResults(shipments) {
    const resultsContainer = document.getElementById('shipment_results');
    const resultsBody = document.getElementById('shipment_results_body');
    
    resultsBody.innerHTML = '';
    
    if (shipments.length === 0) {
        resultsBody.innerHTML = '<tr><td colspan="7" class="text-center">No shipments found</td></tr>';
    } else {
        shipments.forEach(shipment => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <button class="btn btn-sm btn-primary" onclick="selectShipment(${shipment.id}, '${shipment.tracking_number}', '${shipment.client_name || 'N/A'}', '${shipment.origin}', '${shipment.destination}', '${shipment.shipping_cost || 0}')">
                        Select
                    </button>
                </td>
                <td>${shipment.tracking_number}</td>
                <td>${shipment.client_name || 'N/A'}</td>
                <td>${shipment.origin}</td>
                <td>${shipment.destination}</td>
                <td><span class="badge badge-${getStatusColor(shipment.status)}">${shipment.status}</span></td>
                <td>$${parseFloat(shipment.shipping_cost || 0).toFixed(2)}</td>
            `;
            resultsBody.appendChild(row);
        });
    }
    
    resultsContainer.style.display = 'block';
}

function selectShipment(id, tracking, client, origin, destination, cost) {
    selectedShipmentData = {
        id: id,
        tracking_number: tracking,
        client_name: client,
        origin: origin,
        destination: destination,
        shipping_cost: cost
    };
    
    document.getElementById('selected_shipment_id').value = id;
    document.getElementById('selected_tracking').textContent = tracking;
    document.getElementById('selected_client').textContent = client;
    document.getElementById('selected_route').textContent = `${origin} → ${destination}`;
    document.getElementById('selected_cost').textContent = parseFloat(cost).toFixed(2);
    
    document.getElementById('selected_shipment').style.display = 'block';
    document.getElementById('shipment_results').style.display = 'none';
}

function addCharge() {
    const container = document.getElementById('additional_charges_container');
    const chargeItem = document.createElement('div');
    chargeItem.className = 'charge-item';
    chargeItem.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" name="charge_description[]" 
                       placeholder="Description (e.g., Insurance, Handling Fee)">
            </div>
            <div class="col-md-4">
                <input type="number" class="form-control" name="charge_amount[]" 
                       placeholder="Amount" step="0.01">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeCharge(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(chargeItem);
}

function removeCharge(button) {
    button.closest('.charge-item').remove();
}

function generateInvoice() {
    if (!selectedShipmentData) {
        alert('Please select a shipment first');
        return;
    }
    
    const formData = new FormData(document.getElementById('invoiceForm'));
    
    fetch('<?= App\Core\View::url('/api/admin/invoices/generate.php') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('invoice_preview_content').innerHTML = data.html;
            $('#invoicePreviewModal').modal('show');
        } else {
            alert('Error generating invoice: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error generating invoice:', error);
        alert('An error occurred while generating the invoice.');
    });
}

function generateAndDownload() {
    if (!selectedShipmentData) {
        alert('Please select a shipment first');
        return;
    }
    
    const formData = new FormData(document.getElementById('invoiceForm'));
    formData.append('download', '1');
    
    fetch('<?= App\Core\View::url('/api/admin/invoices/generate.php') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `invoice-${selectedShipmentData.tracking_number}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        console.error('Error downloading invoice:', error);
        alert('An error occurred while downloading the invoice.');
    });
}

function downloadInvoicePDF() {
    // Implementation for downloading PDF from preview
    generateAndDownload();
}

function emailInvoice() {
    // Implementation for emailing invoice
    alert('Email functionality will be implemented in the next phase');
}

function loadRecentInvoices() {
    fetch('<?= App\Core\View::url('/api/admin/invoices/recent.php') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.invoices.length > 0) {
                const container = document.getElementById('recent_invoices');
                container.innerHTML = '';
                
                data.invoices.forEach(invoice => {
                    const item = document.createElement('div');
                    item.className = 'recent-invoice-item';
                    item.innerHTML = `
                        <small>
                            <strong>${invoice.tracking_number}</strong><br>
                            ${invoice.created_at} - $${invoice.total_amount}
                        </small>
                    `;
                    container.appendChild(item);
                });
            }
        })
        .catch(error => {
            console.error('Error loading recent invoices:', error);
        });
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'in_transit': 'info',
        'delivered': 'success',
        'delayed': 'danger',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}
</script>
